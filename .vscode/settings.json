{
    // This settings file is not ignored by git.
    "files.exclude": {
        "**/__pycache__": true,
        "src/**/*.so": true
    },
    "search.exclude": {
        // Exclude symbolic links into SAGE_ROOT/pkgs/
        "build/pkgs/*/src": true,
        // Exclude symbolic links into SAGE_ROOT/src/
        "pkgs/sage-conf_pypi/sage_root": true,
        "pkgs/sage-docbuild/sage_docbuild": true,
        "pkgs/sage-setup/sage_setup": true,
        "pkgs/sagemath-*/sage": true,
        "pkgs/sagemath-*/sage_setup": true
    },
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": [
        "--doctest"
    ],
    "python.testing.unittestEnabled": false,
    "cSpell.words": [
        "adic",
        "arccos",
        "arccosh",
        "arcsin",
        "arcsinh",
        "arctan",
        "arctanh",
        "Bejger",
        "bigcup",
        "cachefunc",
        "charpoly",
        "classmethod",
        "clopen",
        "codim",
        "codomain",
        "coframe",
        "coframes",
        "Conda",
        "cputime",
        "cysignals",
        "Cython",
        "d'Alembertian",
        "dalembertian",
        "disp",
        "doctest",
        "doctests",
        "emptyset",
        "figsize",
        "Florentin",
        "fontsize",
        "forall",
        "furo",
        "Gourgoulhon",
        "grayskull",
        "groebner",
        "homeomorphic",
        "homset",
        "homsets",
        "hypersurfaces",
        "infty",
        "Jaffredo",
        "Katsura",
        "Koeppe",
        "longmapsto",
        "longrightarrow",
        "mapsto",
        "mathbb",
        "mathrm",
        "Michal",
        "micjung",
        "Miniconda",
        "Miniforge",
        "Minkowski",
        "Möbius",
        "mpfr",
        "nabla",
        "Nullspace",
        "padics",
        "pari",
        "prandom",
        "Pynac",
        "pyproject",
        "rightarrow",
        "sagemath",
        "scalarfield",
        "sdist",
        "SEEALSO",
        "setminus",
        "smithform",
        "spkg",
        "subchart",
        "subcharts",
        "subframe",
        "subframes",
        "subobjects",
        "subring",
        "superchart",
        "supercharts",
        "supersets",
        "sympy",
        "tensorfield",
        "trigsimp",
        "varphi",
        "vbundle",
        "vecmat",
        "vectorfield",
        "walltime",
        "zmax",
        "zmin"
    ],
    "[python]": {
        "editor.defaultFormatter": "charliermarsh.ruff"
    },
    "esbonio.sphinx.confDir": "",
    // Don't update the settings.json file with values inferred from Meson (we provide them manually)
    "mesonbuild.modifySettings": false,
    // Use the Meson build system for C/C++ files
    "C_Cpp.default.configurationProvider": "mesonbuild.mesonbuild",
    // Use the compile_commands.json file generated by Meson for IntelliSense
    "C_Cpp.default.compileCommands": "${workspaceFolder}/builddir/compile_commands.json"
}
