name: Failure building from source
description: Use this template when reporting a build failure
title: "<title>"
labels: ['c: build', 't: bug']
assignees: []
body:
  - type: markdown
    attributes:
      value: |
        * Please search to see if an issue already exists for the bug you encountered.
        * Please read [README.md](https://github.com/sagemath/sage/blob/develop/README.md) and [the Troubleshooting section in the Installation Guide](https://doc.sagemath.org/html/en/installation/troubles.html).
  - type: textarea
    attributes:
      label: Environment
      description: |
        examples:
          - **OS**: Ubuntu 20.04
          - Sage Version: 9.2
      value: |
          - **OS**: 
          - **Sage Version**: 
    validations:
      required: true
  - type: textarea
    attributes:
      label: Steps To Reproduce
      description: Steps to reproduce the behavior.
      placeholder: |
        1. In this environment...
        2. With this config...
        3. Run '...'
        4. See error...
    validations:
      required: false
  - type: textarea
    attributes:
      label: Config log
      description: |
        Please attach `config.log`.
        Tip: You can attach log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Package logs
      description: |
        Please attach ̀`logs/pkgs/SPKG.log` for failing packages.
        Tip: You can attach log files by clicking this area to highlight it and then dragging files in.
    validations:
      required: false
  - type: textarea
    attributes:
      label: Additional Information
      description: |
        Links? References? Anything that will give us more context about the issue you are encountering!
    validations:
      required: false
  - type: checkboxes
    attributes:
      label: Checklist
      options:
      - label: I have searched the existing issues for a bug report that matches the one I want to file, without success.
        required: true
      - label: I have read the documentation and troubleshoot guide
        required: true
