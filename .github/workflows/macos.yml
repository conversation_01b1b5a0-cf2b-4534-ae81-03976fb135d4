name: Reusable workflow for macOS portability CI

on:
  workflow_call:
    inputs:
      # Either specify a stage
      stage:
        required: false
        type: string
      # Or specify targets
      targets_pre:
        default: build/make/Makefile
        type: string
      targets:
        default: build/make/Makefile
        type: string
      targets_optional:
        default: build/make/Makefile
        type: string
      # System configuration
      osversion_xcodeversion_toxenv_tuples:
        # As of 2024-05, "runs-on: macos-latest" and "runs-on: macos-14" selects the new M1 runners.
        # https://docs.github.com/en/actions/using-github-hosted-runners/about-github-hosted-runners/about-github-hosted-runners#standard-github-hosted-runners-for-public-repositories
        description: 'Stringified JSON object'
        default: >-
          [
           ["latest", "",             "homebrew-macos-opthomebrew-standard"],
          ]
        type: string
      extra_sage_packages:
        description: 'Extra Sage packages to install as system packages'
        type: string
        default: ""
      max_parallel:
        type: number
        default: 10
      free_disk_space:
        default: false
        type: boolean
      timeout:
        description: 'Elapsed time (seconds) at which to kill the build'
        default: 20000
        type: number
      #
      # For use in upstream CIs.
      #
      upstream_artifact:
        required: false
        type: string
      sage_repo:
        required: false
        type: string
      sage_ref:
        required: false
        type: string

jobs:
  local-macos:

    runs-on: macos-${{ matrix.osversion_xcodeversion_toxenv[0] }}
    strategy:
      fail-fast: false
      max-parallel: ${{ inputs.max_parallel }}
      matrix:
        osversion_xcodeversion_toxenv: ${{ fromJson(inputs.osversion_xcodeversion_toxenv_tuples) }}
    env:
      TOX_ENV: local-${{ matrix.osversion_xcodeversion_toxenv[2] }}${{ matrix.osversion_xcodeversion_toxenv[1] && format('-{0}', matrix.osversion_xcodeversion_toxenv[1]) }}
      LOCAL_ARTIFACT_NAME: sage-local-commit-${{ github.sha }}-tox-local-${{ matrix.osversion_xcodeversion_toxenv[2] }}-macos-${{ matrix.osversion_xcodeversion_toxenv[0] }}${{ matrix.osversion_xcodeversion_toxenv[1] && format('-{0}', matrix.osversion_xcodeversion_toxenv[1]) }}
      LOGS_ARTIFACT_NAME: logs-commit-${{ github.sha }}-tox-local-${{ matrix.osversion_xcodeversion_toxenv[2] }}-macos-${{ matrix.osversion_xcodeversion_toxenv[0] }}${{ matrix.osversion_xcodeversion_toxenv[1] && format('-{0}', matrix.osversion_xcodeversion_toxenv[1]) }}-stage${{ inputs.stage }}
    steps:
      - name: Check out SageMath
        uses: actions/checkout@v4
        with:
          repository: ${{ inputs.sage_repo }}
          ref: ${{ inputs.sage_ref }}
          fetch-depth: 10000
      - uses: actions/setup-python@v5
        # As of 2024-02-03, the macOS M1 runners do not have preinstalled python or pipx.
        # Installing pipx follows the approach of https://github.com/pypa/cibuildwheel/pull/1743
        id: python
        with:
          python-version: "3.8 - 3.12"
          update-environment: false
      - name: Install test prerequisites
        run: |
          "${{ steps.python.outputs.python-path }}" -m pip install pipx
      - name: Download upstream artifact
        uses: actions/download-artifact@v4
        with:
          path: upstream
          name: ${{ inputs.upstream_artifact }}
        if: inputs.upstream_artifact
      - name: Update Sage packages from upstream artifact
        run: |
          (export PATH=$(pwd)/build/bin:$PATH; (cd upstream && bash -x update-pkgs.sh) && git diff)
        if: inputs.upstream_artifact

      - name: Merge CI fixes from sagemath/sage
        run: |
          .github/workflows/merge-fixes.sh
        env:
          GH_TOKEN: ${{ github.token }}
          SAGE_CI_FIXES_FROM_REPOSITORIES: ${{ vars.SAGE_CI_FIXES_FROM_REPOSITORIES }}

      - uses: actions/download-artifact@v4
        with:
          path: sage-local-artifact
          name: ${{ env.LOCAL_ARTIFACT_NAME }}
        if: contains(inputs.stage, '2')
      - name: Extract sage-local artifact
        # This is macOS tar -- cannot use --listed-incremental
        run: |
          export SAGE_LOCAL=$(pwd)/.tox/$TOX_ENV/local
          .github/workflows/extract-sage-local.sh sage-local-artifact/sage-local-*.tar
        if: contains(inputs.stage, '2')
      - name: Build and test with tox
        # We use a high parallelization on purpose in order to catch possible parallelization bugs in the build scripts.
        # For doctesting, we use a lower parallelization to avoid timeouts.
        run: |
          case "${{ inputs.stage }}" in
            1)               export TARGETS_PRE="all-sage-local" TARGETS="all-sage-local" TARGETS_OPTIONAL="build/make/Makefile"
                             ;;
            2)               export TARGETS_PRE="all-sage-local" TARGETS="build" TARGETS_OPTIONAL="ptest"
                             ;;
            2-optional*)     export TARGETS_PRE="build/make/Makefile" TARGETS="build/make/Makefile"
                             targets_pattern="${{ inputs.stage }}"
                             targets_pattern="${targets_pattern#2-optional-}"
                             export TARGETS_OPTIONAL=$( echo $(export PATH=build/bin:$PATH && sage-package list :optional: --has-file 'spkg-install.in|spkg-install|requirements.txt' --no-file huge|has_nonfree_dependencies |  grep -v sagemath_doc | grep "^[$targets_pattern]" ) )
                             ;;
            2-experimental*) export TARGETS_PRE="build/make/Makefile" TARGETS="build/make/Makefile"
                             targets_pattern="${{ inputs.stage }}"
                             targets_pattern="${targets_pattern#2-experimental-}"
                             export TARGETS_OPTIONAL=$( echo $(export PATH=build/bin:$PATH && sage-package list :experimental: --has-file 'spkg-install.in|spkg-install|requirements.txt' --no-file huge|has_nonfree_dependencies |  grep -v sagemath_doc | grep "^[$targets_pattern]" ) )
                             ;;
            *)               export TARGETS_PRE="${{ inputs.targets_pre }}" TARGETS="${{ inputs.targets }} TARGETS_OPTIONAL="${{ inputs.targets_optional }}
                             ;;
          esac
          (sleep ${{ inputs.timeout }}; pkill make) &
          MAKE="make -j12" EXTRA_SAGE_PACKAGES="${{ inputs.extra_sage_packages }}" "${{ steps.python.outputs.python-path }}" -m pipx run tox -e $TOX_ENV -- SAGE_NUM_THREADS=6 $TARGETS
      - name: Prepare logs artifact
        run: |
          mkdir -p "artifacts/$LOGS_ARTIFACT_NAME"; cp -r .tox/*/log "artifacts/$LOGS_ARTIFACT_NAME"
        if: always()
      - uses: actions/upload-artifact@v4
        with:
          path: artifacts
          name: ${{ env.LOGS_ARTIFACT_NAME }}
        if: always()
      - name: Print out logs for immediate inspection
        # and markup the output with GitHub Actions logging commands
        run: |
          .github/workflows/scan-logs.sh "artifacts/$LOGS_ARTIFACT_NAME"
        if: always()
      - name: Prepare sage-local artifact
        # This also includes the copies of homebrew or conda installed in the tox environment.
        # We use absolute pathnames in the tar file.
        # This is macOS tar -- cannot use --remove-files.
        # We remove the $SAGE_LOCAL/lib64 link, which will be recreated by the next stage.
        run: |
          mkdir -p sage-local-artifact && (cd .tox/$TOX_ENV && rm -f "local/lib64" && tar -cf - $(pwd)) > sage-local-artifact/sage-${{ env.TOX_ENV }}-${{ inputs.stage }}.tar
        if: contains(inputs.stage, '1')
      - uses: actions/upload-artifact@v4
        with:
          path: sage-local-artifact/sage-${{ env.TOX_ENV }}-${{ inputs.stage }}.tar
          name: ${{ env.LOCAL_ARTIFACT_NAME }}
        if: contains(inputs.stage, '1')
