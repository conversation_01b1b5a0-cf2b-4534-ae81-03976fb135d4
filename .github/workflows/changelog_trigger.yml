name: Trigger Changelog Generation

on:
  release:
    types: [published]

jobs:
  trigger-website-repo-workflow:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger Generate Changelog Workflow in website repo
        if: "!github.event.release.prerelease"
        env:
          GITHUB_PAT: ${{ secrets.WEBSITE_ACCESS_TOKEN }}
          RELEASE_TAG: ${{ github.event.release.tag_name }}
        run: |
          curl -L \
            -X POST \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer $GITHUB_PAT" \
            -H "X-GitHub-Api-Version: 2022-11-28" \
            https://api.github.com/repos/sagemath/website/actions/workflows/generate_changelog.yml/dispatches \
            -d '{"ref":"master","inputs":{"release_tag":"'"$RELEASE_TAG"'"}}'
