name: Reusable workflow for Docker Hub images

on:
  workflow_call:
    inputs:
      dockerhub_repository:
        default: sagemath-dev
        type: string
      dockerfile_target:
        default: make-build
        type: string

env:
  CAN_LOGIN: ${{ secrets.DOCKERHUB_USERNAME != '' && secrets.DOCKERHUB_TOKEN != '' }}

jobs:
  build-and-push:
    name: Build Docker image and push to DockerHub
    runs-on: ubuntu-latest
    steps:
      - name: Maximize build disk space
        uses: easimon/maximize-build-space@v10
        with:
          # need space in /var for Docker images
          root-reserve-mb:      30000
          remove-dotnet:        true
          remove-android:       true
          remove-haskell:       true
          remove-codeql:        true
          remove-docker-images: true
        continue-on-error: true

      - name: Checkout
        uses: actions/checkout@v4

      - name: Set tag
        # docker/metadata-action@v4 is not used since we need to distinguish
        # between latest and develop tags
        id: set_tag
        run: |
          git fetch --depth=1 origin +refs/tags/*:refs/tags/*
          TAG_NAME=$(git tag --sort=creatordate | tail -1)
          REPO=${{ inputs.dockerhub_repository }}
          # see if the tag has already been pushed
          # if yes then skip following steps
          URL="https://registry.hub.docker.com/v2/repositories/sagemath/$REPO/tags?page_size=32"
          LATEST_TAGS=$(curl -L -s $URL | jq '."results"[]["name"]')
          JOB_DONE=false
          for i in $LATEST_TAGS; do if [[ $i == \"$TAG_NAME\" ]]; then JOB_DONE=true; break; fi done
          echo "JOB_DONE=$JOB_DONE" >> $GITHUB_ENV

          if [[ $JOB_DONE == 'false' ]]
          then
            TAG="sagemath/$REPO:$TAG_NAME"
            TAG_LIST="$TAG, sagemath/$REPO:develop"
            BASE="sagemath/sagemath-dev:$TAG_NAME"
            echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
            echo "TAG=$TAG" >> $GITHUB_ENV
            echo "TAG_LIST=$TAG_LIST" >> $GITHUB_ENV
            echo "BASE=$BASE" >> $GITHUB_ENV
          fi
          df -h

      - name: Update Tag List
        id: upd_tag_list
        run: |
          REPO=${{ inputs.dockerhub_repository }}
          TAG_LIST="${{ env.TAG_LIST }}, sagemath/$REPO:latest"
          echo "TAG_LIST=$TAG_LIST" >> $GITHUB_ENV
        if: "env.JOB_DONE == 'false' && !contains(env.TAG_NAME, 'beta') && !contains(env.TAG_NAME, 'rc')"

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        if: env.JOB_DONE == 'false'

      - name: Login to Docker Hub
        id: login
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        if: env.JOB_DONE == 'false' && env.CAN_LOGIN == 'true'

      - name: Build and push make-build
        uses: docker/build-push-action@v6
        with:
          context: .
          file: docker/Dockerfile
          target: ${{ inputs.dockerfile_target }}
          build-args: |
            MAKE_BUILD=${{ env.BASE }}
          push: ${{ steps.login.outcome == 'success' }}
          tags: ${{ env.TAG_LIST }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
        # run even if there are no push credentials (just don't push)
        if: (success() || failure()) && env.JOB_DONE == 'false'
