name: CI macOS

## This GitHub Actions workflow runs SAGE_ROOT/tox.ini with select environments,
## whenever a GitHub pull request is opened or synchronized in a repository
## where GitHub Actions are enabled.
##
## It builds and checks some sage spkgs as defined in TARGETS.
##
## A job succeeds if there is no error.
##
## The build is run with "make V=0", so the build logs of individual packages are suppressed.
##
## At the end, all package build logs that contain an error are printed out.
##
## After all jobs have finished (or are canceled) and a short delay,
## tar files of all logs are made available as "build artifacts".

#on: [push, pull_request]

on:
  pull_request:
    paths:
      - '.github/workflows/ci-macos.yml'
      - '.github/workflows/macos.yml'
  push:
    tags:
      - '*'
  workflow_dispatch:
    # Allow to run manually

env:
  TARGETS_PRE: all-sage-local
  TARGETS: build
  TARGETS_OPTIONAL: ptest

jobs:
  stage-1:
    uses: ./.github/workflows/macos.yml
    with:
      stage: "1"
      timeout: 14400

  stage-2:
    uses: ./.github/workflows/macos.yml
    with:
      stage: "2"
    needs: [stage-1]
    if: ${{ success() || failure() }}

  stage-2-optional-0-o:
    uses: ./.github/workflows/macos.yml
    with:
      stage: "2-optional-0-o"
    needs: [stage-2]
    if: ${{ success() || failure() }}

  stage-2-optional-p-z:
    uses: ./.github/workflows/macos.yml
    with:
      stage: "2-optional-p-z"
    needs: [stage-2-optional-0-o]
    if: ${{ success() || failure() }}

  stage-2-experimental-0-o:
    uses: ./.github/workflows/macos.yml
    with:
      stage: "2-experimental-0-o"
    needs: [stage-2-optional-p-z]
    if: ${{ success() || failure() }}

  stage-2-experimental-p-z:
    uses: ./.github/workflows/macos.yml
    with:
      stage: "2-experimental-p-z"
    needs: [stage-2-experimental-0-o]
    if: ${{ success() || failure() }}

  dist:

    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 500
      - name: fetch tags
        run: git fetch --depth=1 origin +refs/tags/*:refs/tags/*
      - name: Install bootstrap prerequisites
        run: |
          sudo DEBIAN_FRONTEND=noninteractive apt-get update
          sudo DEBIAN_FRONTEND=noninteractive apt-get install $(build/bin/sage-get-system-packages debian _bootstrap _prereq)
      - name: Bootstrap with update-version
        # We set SAGE_ROOT and SAGE_SRC by hand
        # because 'sage -sh' does not work with an unconfigured tree,
        # giving: Error: SAGE_SCRIPTS_DIR is set to a bad value
        run: |
          git config --global user.email "<EMAIL>"
          git config --global user.name "Sage GitHub CI"
          SAGE_ROOT=. SAGE_SRC=./src tools/update-version $(cat src/VERSION.txt).dev0 || echo "(ignoring error)"
      - name: make dist
        run: |
          ./configure --enable-download-from-upstream-url && make dist
      - uses: actions/upload-artifact@v4
        with:
          path: "dist/*.tar.gz"
          name: dist
