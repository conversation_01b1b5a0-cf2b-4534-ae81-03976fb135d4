name: CI Linux incremental

## This GitHub Actions workflow runs SAGE_ROOT/tox.ini with selected environments,
## whenever a GitHub pull request is opened or synchronized in a repository
## where GitHub Actions are enabled.
##
## It builds and checks some sage spkgs as defined in TARGETS.
##
## A job succeeds if there is no error.
##
## The build is run with "make V=0", so the build logs of individual packages are suppressed.
##
## At the end, all package build logs that contain an error are printed out.
##
## After all jobs have finished (or are canceled) and a short delay,
## tar files of all logs are made available as "build artifacts".

on:
  pull_request:
    paths:
      - '.github/workflows/ci-linux-incremental.yml'
      - 'build/pkgs/**'
      - 'configure.ac'
      - '!build/pkgs/sage_conf/**'
      - '!build/pkgs/sage_docbuild/**'
      - '!build/pkgs/sage_setup/**'
      - '!build/pkgs/sagelib/**'
      - '!build/pkgs/sagemath_*/**'
  workflow_dispatch:

concurrency:
  # Cancel previous runs of this workflow for the same branch
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

permissions:
  packages: write

jobs:

  changed_files:
    runs-on: ubuntu-latest
    name: List changed packages
    outputs:
      build_targets:     ${{ steps.build-targets.outputs.build_targets }}
    steps:
      - uses: actions/checkout@v4
      - name: Get all packages that have changed
        id: changed-files
        uses: tj-actions/changed-files@v46
        with:
          files_yaml: |
            configures:
              - 'build/pkgs/*/spkg-configure.m4'
            pkgs:
              - 'build/pkgs/**'
              - 'pkgs/**'
      - name: Determine targets to build
        id: build-targets
        run: |
          uninstall_targets=$(echo $(for a in '' ${{ steps.changed-files.outputs.configures_all_changed_files }}; do echo $a | sed -E 's,build/pkgs/([a-z0-9][_.a-z0-9]*)/spkg-configure[.]m4 *,\1-uninstall,'; done | sort -u))
          build_targets=$(echo $(for a in '' ${{ steps.changed-files.outputs.pkgs_all_changed_files }}; do SPKG=$(echo $a | sed -E 's,-,_,g;s,(build/)?pkgs/([a-z0-9][-_.a-z0-9]*)/[^ ]* *,\2,;'); if [ -f "build/pkgs/$SPKG/checksums.ini" -o -f "build/pkgs/$SPKG/requirements.txt" -o -f "build/pkgs/$SPKG/spkg-install" ]; then echo "$SPKG-ensure"; fi; done | sort -u))
          if [ -n "$uninstall_targets" ]; then
              echo "build_targets=$uninstall_targets reconfigure $build_targets" >> $GITHUB_OUTPUT
          else
              echo "build_targets=$build_targets" >> $GITHUB_OUTPUT
          fi
          cat $GITHUB_OUTPUT
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.base_ref }}
          path: worktree-base
        if: github.base_ref && steps.changed-files.outputs.pkgs_all_changed_files
      - name: Compute metrics
        run: |
          export PATH=build/bin:$PATH
          if [ -d worktree-base ]; then
            (echo "# $GITHUB_BASE_REF"; SAGE_ROOT=worktree-base sage-package metrics :all:) > base-metrics.txt
            (echo "# $GITHUB_REF"; sage-package metrics :all:) > metrics.txt
            diff --color=always --width=100 --side-by-side --left-column base-metrics.txt metrics.txt || true
          else
            sage-package metrics :all:
          fi

  test:
    needs: [changed_files]
    uses: ./.github/workflows/docker.yml
    with:
      # Build incrementally from published Docker image
      incremental: true
      free_disk_space: true
      from_docker_repository: ghcr.io/sagemath/sage/
      from_docker_target: "with-targets"
      from_docker_tag: "dev"
      docker_targets: "with-targets"
      targets: "${{needs.changed_files.outputs.build_targets}} ci-build-with-fallback ptest-nodoc"
      tox_system_factors: >-
        ["ubuntu-focal",
         "ubuntu-jammy",
         "debian-bullseye",
         "debian-bookworm",
         "fedora-41",]
      tox_packages_factors: >-
          ["standard"]
      docker_push_repository: ghcr.io/${{ github.repository }}/
      max_parallel: 8
