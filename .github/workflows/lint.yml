name: Lint

on:
  push:
    branches:
      - master
      - develop
  pull_request:
  merge_group:

concurrency:
  # Cancel previous runs of this workflow for the same branch
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  lint:
    name: Lint
    runs-on: ubuntu-latest
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Merge CI fixes from sagemath/sage
      run: |
        .github/workflows/merge-fixes.sh
      env:
        GH_TOKEN: ${{ github.token }}

    - name: Install prerequisites
      id: deps
      run: pip install uv

    - name: Code style check with ruff-minimal
      if: (success() || failure()) && steps.deps.outcome == 'success'
      run: |
        uv run --frozen --only-group lint -- ruff check --output-format github --ignore E402,E721,E731,E741,E742,E743,F401,F402,F403,F405,F821,F841,I001,<PERSON><PERSON>0206,<PERSON><PERSON>0208,<PERSON><PERSON>2401,<PERSON><PERSON>3002,<PERSON><PERSON>0302,<PERSON><PERSON><PERSON>24,<PERSON><PERSON>0402,<PERSON><PERSON>0911,<PERSON><PERSON>0912,<PERSON><PERSON>0913,<PERSON><PERSON>0915,<PERSON><PERSON>1704,<PERSON><PERSON>1711,<PERSON><PERSON>1714,<PERSON><PERSON>1716,P<PERSON>1736,PLR2004,PLR5501,PLW0120,PLW0211,PLW0602,PLW0603,PLW0642,PLW1508,PLW1510,PLW2901,PLW3301
        uv run --frozen --only-group lint -- ruff check --output-format github --preview --select E111,E115,E21,E221,E222,E225,E227,E228,E25,E271,E272,E275,E302,E303,E305,E306,E401,E502,E701,E702,E703,E71,W291,W293,W391,W605 src/sage/

    - name: Code style check with relint
      if: (success() || failure()) && steps.deps.outcome == 'success'
      run: uv run --frozen --only-group lint -- relint -c src/.relint.yml -- src/sage/

    - name: Validate docstring markup as RST
      if: (success() || failure()) && steps.deps.outcome == 'success'
      run: uv run --frozen --only-group lint -- flake8 --select=RST src/sage/ --config src/tox.ini
