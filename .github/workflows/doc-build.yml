name: Build documentation

on:
  pull_request:
  merge_group:
  push:
    tags:
      # Match all release tags including beta, rc
      - '[0-9]+.[0-9]+'
      - '[0-9]+.[0-9]+.[0-9]+'
      - '[0-9]+.[0-9]+.beta[0-9]+'
      - '[0-9]+.[0-9]+.[0-9]+.beta[0-9]+'
      - '[0-9]+.[0-9]+.rc[0-9]+'
      - '[0-9]+.[0-9]+.[0-9]+.rc[0-9]+'
    branches:
      - develop
  workflow_dispatch:
    # Allow to run manually

concurrency:
  # Cancel previous runs of this workflow for the same branch
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PYTHON_VERSION: 3.11

jobs:
  build-doc:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Merge CI fixes from sagemath/sage
        run: |
          .github/workflows/merge-fixes.sh
        env:
          GH_TOKEN: ${{ github.token }}

      - name: Cache conda packages
        uses: actions/cache@v4
        with:
          path: ~/conda_pkgs_dir
          key:
            ${{ runner.os }}-conda-${{ hashFiles('environment-${{ env.PYTHON_VERSION }}-linux.yml') }}
  
      - name: Compiler cache
        uses: hendrikmuhs/ccache-action@v1.2
        with:
          key: ${{ runner.os }}-meson-${{ env.PYTHON_VERSION }}

      - name: Setup Conda environment
        uses: conda-incubator/setup-miniconda@v3
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          # Disabled for now due to
          # https://github.com/conda-incubator/setup-miniconda/issues/379
          # miniforge-version: latest
          use-mamba: true
          channels: conda-forge
          channel-priority: true
          activate-environment: sage-dev
          environment-file: environment-${{ env.PYTHON_VERSION }}-linux.yml
        
      - name: Build Sage
        shell: bash -l {0}
        run: |
          export PATH="/usr/lib/ccache:/usr/local/opt/ccache/libexec:$PATH"
          export CC="ccache $CC"
          export CXX="ccache $CXX"
          pip install --no-build-isolation --config-settings=builddir=builddir --editable . -v

      #
      # For pull requests
      #
      - name: Get workflow run-id
        id: get_run_id
        if: github.event_name == 'pull_request'
        run: |
          RESPONSE=$(curl -s -L \
            -H "Accept: application/vnd.github+json" \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/actions/runs?event=push&branch=develop&status=completed")
          RUN_ID=$(echo "$RESPONSE" | jq -r --arg name "${{ github.workflow }}" --arg conclusion "success" \
            '.workflow_runs[] | select(.name == $name and .conclusion == $conclusion) | .id' | head -n 1)
          echo "RUN_ID=$RUN_ID" >> $GITHUB_ENV

      - name: Download old doc
        id: download-doc
        if: steps.get_run_id.outcome == 'success'
        uses: actions/download-artifact@v4
        with:
          name: doc-develop
          github-token: ${{ secrets.GITHUB_TOKEN }}
          repository: ${{ github.repository }}
          run-id: ${{ env.RUN_ID }}

      - name: Store old doc
        id: worktree
        if: steps.download-doc.outcome == 'success'
        shell: bash -l {0}
        run: |
          git config --global --add safe.directory $(pwd)
          git config --global user.email "<EMAIL>"
          git config --global user.name "Build documentation workflow"
          unzip doc.zip
          rm doc.zip
          PR_NUMBER=""
          if [[ "$GITHUB_REF" =~ refs/pull/([0-9]+)/merge ]]; then
            PR_NUMBER="${BASH_REMATCH[1]}"
          fi
          # Create CHANGES.html
          if [[ -n "$PR_NUMBER" ]]; then
            # mathjax path in old doc (regex)
            mathjax_path_from="[-./A-Za-z_]*/tex-chtml[.]js?v=[0-9a-f]*"
            # mathjax path in new doc
            mathjax_path_to=$(SAGE_USE_CDNS=yes python -c "from src.sage_docbuild.conf import mathjax_path; print(mathjax_path)")
            new_version=$(cat src/VERSION.txt)
            # Wipe out chronic diffs between old doc and new doc
            (cd doc && \
             find . -name "*.html" | xargs sed -i -e '/class="sidebar-brand-text"/ s/Sage [0-9a-z.]* /Sage '"$new_version"' /' \
                                                  -e 's;?v=[0-9a-f]*";";' \
                                                  -e 's;'"$mathjax_path_from"';'"$mathjax_path_to"';' \
                                                  -e '\;<script type="application/vnd\.jupyter\.widget-state+json">;,\;</script>; d' \
                                                  -e 's;#L[0-9]*";";' \
                                                  -e 's;tab-set--[0-9]*-;tab-set-;g' \
                                                  -e 's;"tab-set--[0-9]*";"tab-set";' \
             && true)
            # If the regex list above is changed, the sed command in "Copy doc" step should also be changed
            # Create git repo from old doc
            (cd doc && \
             git init && \
             (echo "*.svg binary"; echo "*.pdf binary") > .gitattributes && \
             (echo ".buildinfo"; echo '*.inv'; echo '.git*'; echo '*.svg'; echo '*.pdf'; echo '*.png'; echo 'searchindex.js') > .gitignore && \
             git add -A && git commit --quiet -m 'old')
          fi

      - name: Build documentation
        id: docbuild
        if: steps.worktree.outcome == 'success'
        shell: bash -l {0}
        run: |
          meson compile -C builddir doc-html
        env:
          SAGE_USE_CDNS: yes
          SAGE_DOCBUILD_OPTS: "--include-tests-blocks"

      - name: Copy doc
        id: copy
        if: steps.docbuild.outcome == 'success'
        run: |
          set -ex
          # Remove any existing html directory before copying a new one
          if [ -d "doc/html" ]; then
              rm -rf doc/html
          fi
          cp -r builddir/src/doc/* doc/
          # Check if we are on pull request event
          PR_NUMBER=""
          if [[ -n "$GITHUB_REF" ]]; then
            if [[ "$GITHUB_REF" =~ refs/pull/([0-9]+)/merge ]]; then
              PR_NUMBER="${BASH_REMATCH[1]}"
            fi
          fi
          # If so, then create CHANGES.html
          if [[ -n "$PR_NUMBER" ]]; then
            (cd doc && git add -A && git commit --quiet -m 'new')
            # Wipe out chronic diffs of new doc against old doc before creating CHANGES.html
            (cd doc && \
             find . -name "*.html" | xargs sed -i -e '/This is documentation/ s/ built with GitHub PR .* for development/ for development/' \
                                                  -e 's;?v=[0-9a-f]*";";' \
                                                  -e '\;<script type="application/vnd\.jupyter\.widget-state+json">;,\;</script>; d' \
                                                  -e 's;#L[0-9]*";";' \
                                                  -e 's;tab-set--[0-9]*-;tab-set-;g' \
                                                  -e 's;"tab-set--[0-9]*";"tab-set";' \
             && git commit -a -m 'wipe-out')
            # If the regex list above is changed, the sed command in "Store old doc" step should also be changed
            # Since HEAD is at commit 'wipe-out', HEAD~1 is commit 'new' (new doc), HEAD~2 is commit 'old' (old doc)
            (cd doc && git diff $(git rev-parse HEAD~2) -- "*.html") > diff.txt
            # Restore the new doc dropping changes by "wipe out"
            (cd doc && git checkout --quiet -f HEAD~1)
            .github/workflows/create-changes-html.sh diff.txt doc
            # Sometimes rm -rf .git errors out because of some diehard hidden files
            # So we simply move it out of the doc directory
            (cd doc && mv .git ../git && mv .gitattributes ../gitattributes)
            mv CHANGES.html doc
          fi
          # Create the robots.txt file to discourage web crawlers from indexing doc preview webpages
          echo "User-agent: *" > doc/robots.txt
          echo "Disallow: /" >> doc/robots.txt
          # Zip everything for increased performance
          zip -r doc.zip doc

      - name: Upload doc
        id: upload
        if: steps.copy.outcome == 'success'
        uses: actions/upload-artifact@v4
        with:
          name: doc
          path: doc.zip

      - name: Upload doc-develop
        # artifact doc-develop is used for doc build on pull request event
        id: upload-push
        if: steps.copy.outcome == 'success' && github.event_name == 'push'
        uses: actions/upload-artifact@v4
        with:
          name: doc-${{ github.ref_name }}
          path: doc.zip

      #
      # On release tag event
      #

      - name: Build live doc
        id: buildlivedoc
        if: startsWith(github.ref, 'refs/tags/')
        shell: bash -l {0}
        run: |
          meson compile -C builddir doc-html
        env:
          SAGE_USE_CDNS: yes
          SAGE_LIVE_DOC: yes
          SAGE_JUPYTER_SERVER: binder:sagemath/sage-binder-env/dev
          SAGE_DOCBUILD_OPTS: "--include-tests-blocks"

      - name: Copy live doc
        id: copylivedoc
        if: steps.buildlivedoc.outcome == 'success'
        run: |
          mkdir -p ./livedoc
          # We copy everything to a local folder
            cp -r builddir/src/doc/html livedoc/
            cp -r builddir/src/doc/pdf livedoc/
            cp builddir/src/doc/index.html livedoc/
          zip -r livedoc.zip livedoc

      - name: Upload live doc
        if: steps.copylivedoc.outcome == 'success'
        uses: actions/upload-artifact@v4
        with:
          name: livedoc
          path: livedoc.zip

