name: CI Linux

on:
  push:
    tags:
      - '*'
  pull_request:
    paths:
      - '**.build'
      - 'subprojects/**'
  workflow_dispatch:
    # Allow to run manually

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        container:
          - fedora:41
          - fedora:42
    container:
      image: ${{ matrix.container }}

    steps:
      - name: Checkout code
        # cannot use v4 yet because of https://github.com/actions/checkout/issues/1487
        uses: actions/checkout@v3

      - name: Install uv
        uses: astral-sh/setup-uv@v6.3.1

      # We cannot use the setup python action because it doesn't support all containers
      # https://github.com/actions/setup-python/issues/527
      - name: Set up Python
        run: |
          uv python install
          uv venv
          . .venv/bin/activate
          echo PATH=$PATH >> $GITHUB_ENV

      - name: Install dependencies
        run: |
          SYSTEM=$(build/bin/sage-guess-package-system)
          if [ "$SYSTEM" = "fedora" ]; then
              dnf install -y git

              # Need to use --setopt=tsflags="" to avoid errors with gphelp
              dnf install -y pari-gp --setopt=tsflags=""

              # Mitigate upstream packaging bug: https://bugzilla.redhat.com/show_bug.cgi?id=2332429
              # by swapping the incorrectly installed OpenCL-ICD-Loader for the expected ocl-icd
              dnf -y swap --repo='fedora' OpenCL-ICD-Loader ocl-icd
          fi

          eval $(build/bin/sage-print-system-package-command $SYSTEM update)
          eval $(build/bin/sage-print-system-package-command $SYSTEM --yes --ignore-missing install $(build/bin/sage-get-system-packages $SYSTEM $(uv run --no-project build/bin/sage-package list :standard:)))

      - name: Build
        run: |
          # Install build dependencies manually as workaround for https://github.com/astral-sh/uv/issues/1516
          uv pip install \
              meson-python \
              "cypari2 >=2.2.1" \
              "cython >=3.0, != 3.0.3, != 3.1.0" \
              "cython >=3.0, != 3.0.3" \
              "gmpy2 ~=2.1.b999" \
              memory_allocator \
              "numpy >=1.25" \
              jinja2 \
              setuptool
          uv sync --frozen --inexact --no-build-isolation

      - name: Test
        run: |
          uv run ./sage -t --all -p4 || true
        