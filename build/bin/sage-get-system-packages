#!/bin/sh

SYSTEM=$1
if [ -z "$SYSTEM" ]; then
   echo >&2 "usage: $0 {auto|debian|arch|conda|pip|...} SPKGS..."
   exit 1
fi
shift
SPKGS="$*"
#
if [ -z "$SAGE_ROOT" ]; then
    SAGE_ROOT=`pwd`
fi

case "$SYSTEM" in
    install-requires)
        # Collect from src/pyproject.toml or from version_requirements.txt (falling back to requirements.txt) and output it in the format
        # needed by setup.cfg [options] install_requires=
        SYSTEM_PACKAGES_FILE_NAMES="src/pyproject.toml version_requirements.txt requirements.txt"
        # also normalizes quotes from "" to ''.
        STRIP_COMMENTS="sed s/#.*//;/^[[:space:]]*$/d;s/\"/'/g;"
        COLLECT=
        ;;
    install-requires-toml)
        # Collect from src/pyproject.toml or from version_requirements.txt (falling back to requirements.txt) and output it in the format
        # needed by pyproject.toml [build-system] requires=
        SYSTEM_PACKAGES_FILE_NAMES="src/pyproject.toml version_requirements.txt requirements.txt"
        # also normalizes quotes from '' to "".
        STRIP_COMMENTS="sed s/#.*//;/^[[:space:]]*$/d;s/'/\"/g;s/^/'/;s/$/',/;"
        COLLECT=
        ;;
    pip)
        SYSTEM_PACKAGES_FILE_NAMES="requirements.txt src/pyproject.toml version_requirements.txt"
        STRIP_COMMENTS='sed s/#.*//;s/[[:space:]]//g;'
        COLLECT=echo
        ;;
    versions)
        # For use in sage-spkg-info
        SYSTEM_PACKAGES_FILE_NAMES="package-version.txt requirements.txt src/pyproject.toml version_requirements.txt"
        strip_comments () {
            TEXT=$(sed "s/#.*//;/^[[:space:]]*$/d;s/\"/'/g;s/^/    /;" "$@")
            if [ -n "$(echo $TEXT)" ]; then
                echo "$NAME::"
                echo
                echo "$TEXT"
                echo
            fi
        }
        STRIP_COMMENTS=strip_comments
        COLLECT=
        ;;
    *)
        if [ "$SYSTEM" = auto ]; then
            SYSTEM=$(sage-guess-package-system 2>/dev/null)
            if [ "$SYSTEM" = unknown ]; then
                echo >&2 "unknown package system"
                exit 1
            fi
        fi
        SYSTEM_PACKAGES_FILE_NAMES="distros/$SYSTEM.txt"
        STRIP_COMMENTS="sed s/#.*//;s/\${PYTHON_MINOR}/${PYTHON_MINOR}/g"
        COLLECT=echo
        ;;
esac

case "$SPKGS" in
    *pkg:*|pypi/*|generic/*)
        PATH="${SAGE_ROOT}/build/bin:$PATH" SPKGS=$(sage-package list $SPKGS)
        ;;
esac

for PKG_BASE in $SPKGS; do
    case "$SYSTEM:$ENABLE_SYSTEM_SITE_PACKAGES" in
        versions*)
            # Show everything.
            ;;
        install-requires*|pip*)
            # This is output for installation of packages into a Python environment.
            # So it's OK to use any Python packages.
            ;;
        *:)
            # --enable-system-site-packages was NOT passed to configure
            # (or script was run outside of the sage-build-env).
            #
            # Skip this package if it uses the SAGE_PYTHON_PACKAGE_CHECK macro.
            #
            SPKG_CONFIGURE="${SAGE_ROOT}/build/pkgs/${PKG_BASE}/spkg-configure.m4"
            if grep -q SAGE_PYTHON_PACKAGE_CHECK "${SPKG_CONFIGURE}" 2>/dev/null;
            then
                continue
            fi
            ;;
        *)
            # --enable-system-site-packages was passed to configure.
            # So any package (Python, non-Python) is fine.
            ;;
    esac

    for NAME in $SYSTEM_PACKAGES_FILE_NAMES; do
        case $NAME in
            *pyproject.toml)
                SYSTEM_PACKAGES_FILE="$SAGE_ROOT"/$NAME
                if [ -f "$SYSTEM_PACKAGES_FILE" ]; then
                    # Extract from the "requires" block in src/pyproject.toml
                    # Packages are in the format "'sage-conf ~= 10.3b3',"
                    PACKAGE_INFO=$(sed -n '/requires *= *\[/,/^\]/s/^ *'\''\('$PKG_BASE'.*\)'\'',/\1/p' "$SAGE_ROOT/src/pyproject.toml")
                    if [ -n "$PACKAGE_INFO" ]; then
                        echo "$PACKAGE_INFO" | ${STRIP_COMMENTS}
                        [ $SYSTEM = versions ] || break
                    fi
                fi
                ;;
            *)
                SYSTEM_PACKAGES_FILE="$SAGE_ROOT"/build/pkgs/$PKG_BASE/$NAME
                if [ -f $SYSTEM_PACKAGES_FILE ]; then
                    if [ -z "$COLLECT" ]; then
                        ${STRIP_COMMENTS} $SYSTEM_PACKAGES_FILE
                    else
                        $COLLECT $(${STRIP_COMMENTS} $SYSTEM_PACKAGES_FILE)
                    fi
                    [ $SYSTEM = versions ] || break
                fi
                ;;
        esac
    done
done
