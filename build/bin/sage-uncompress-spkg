#!/usr/bin/env sage-bootstrap-python

# usage: sage-uncompress-spkg [-h] [-d DIR] PKG [FILE]
#
# positional arguments:
#   PKG         the archive to extract
#   FILE        (deprecated) print the contents of the given archive member to
#               stdout
#
# optional arguments:
#   -h, --help  show this help message and exit
#   -d DIR      directory to extract archive contents into


try:
    import sage_bootstrap
except ImportError:
    import os, sys
    sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))
    import sage_bootstrap

from sage_bootstrap.uncompress.cmdline import run
run()
