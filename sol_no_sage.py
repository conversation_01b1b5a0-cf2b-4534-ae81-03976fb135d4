from Crypto.Util.number import long_to_bytes, inverse
import sys

# Provided values
n = 144984891276196734965453594256209014778963203195049670355310962211566848427398797530783430323749867255090629853380209396636638745366963860490911853783867871911069083374020499249275237733775351499948258100804272648855792462742236340233585752087494417128391287812954224836118997290379527266500377253541233541409
c = 120266872496180344790010286239079096230140095285248849852750641721628852518691698502144313546787272303406150072162647947041382841125823152331376276591975923978272581846998438986804573581487790011219372437422499974314459242841101560412534631063203123729213333507900106440128936135803619578547409588712629485231
hint = 867001369103284883200353678854849752814597815663813166812753132472401652940053476516493313874282097709359168310718974981469532463276979975446490353988
e = 65537

# Configuration
known_bits = 500
total_phi_bits = 536  # Try guessing 36 unknown bits
missing_bits = total_phi_bits - known_bits

print(f"[+] Starting brute-force over {missing_bits} bits...")

max_iter = 1 << missing_bits
for x in range(max_iter):
    phi_candidate = (x << known_bits) | hint
    try:
        d = inverse(e, phi_candidate)
        m = pow(c, d, n)
        flag = long_to_bytes(m)
        if b"flag" in flag or b"uiuctf" in flag:
            print("\n[+] Found flag!")
            print(flag.decode(errors='ignore'))
            print(f"[*] phi candidate: {phi_candidate}")
            break
    except ValueError:
        continue
