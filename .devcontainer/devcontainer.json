// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.245.2/containers/debian
{
	"name": "Conda",
	"image": "mcr.microsoft.com/vscode/devcontainers/base:0-bullseye",

	// Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	"remoteUser": "vscode",

	// Setup conda environment
	"onCreateCommand": ".devcontainer/onCreate-conda.sh || true",

	// Install additional features.
	"features": {
		// For config options, see https://github.com/devcontainers/features/tree/main/src/conda
		"ghcr.io/devcontainers/features/conda": {
            "version": "latest",
			"addCondaForge": "true"
        }
	},
	"customizations": {
		"vscode": {
			"extensions": [
				"guyskk.language-cython",
				"ms-toolsai.jupyter",
				"ms-python.vscode-pylance",
				"ms-python.python",
				"lextudio.restructuredtext",
				"trond-snekvik.simple-rst",
				"charliermarsh.ruff"
			]
		}
	}
}
